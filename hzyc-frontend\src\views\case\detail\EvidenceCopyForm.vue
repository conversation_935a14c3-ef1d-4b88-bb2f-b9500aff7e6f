<template>
  <div class="document-content">
    <div class="content-section">
      <div class="section-header">
        <div class="section-title">证据复制（提取）单</div>
        <el-button 
          size="small" 
          type="primary" 
          @click="toggleEdit"
        >
          {{ isEditing ? '保存' : '编辑' }}
        </el-button>
      </div>

      <!-- 文书标题 -->
      <div class="document-title">
        <div class="bureau-name">
          <template v-if="isEditing">
            <el-input v-model="formData.bureauName" size="small" style="width: 200px;" />
          </template>
          <template v-else>
            <span class="underline-text">{{ formData.bureauName }}</span>
          </template>
          烟草专卖局
        </div>
        <h2>证据复制（提取）单</h2>
      </div>

      <!-- 证据粘贴处 -->
      <div class="evidence-section">
        <div class="evidence-box">
          <div class="evidence-header">
            <span class="evidence-label">（证据粘贴处）</span>
            <div class="evidence-controls" v-if="isEditing">
              <el-button size="small" @click="addEvidenceItem">+</el-button>
            </div>
          </div>
          <div class="evidence-content">
            <template v-if="isEditing">
              <div 
                v-for="(item, index) in formData.evidenceItems" 
                :key="index"
                class="evidence-item"
              >
                <el-input 
                  v-model="item.content" 
                  type="textarea" 
                  :rows="3"
                  placeholder="请输入证据内容..."
                  style="width: 100%;"
                />
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="removeEvidenceItem(index)"
                  style="margin-top: 10px;"
                >
                  删除
                </el-button>
              </div>
            </template>
            <template v-else>
              <div 
                v-for="(item, index) in formData.evidenceItems" 
                :key="index"
                class="evidence-display"
              >
                {{ item.content }}
              </div>
            </template>
          </div>
        </div>
      </div>

      <!-- 说明事项 -->
      <div class="description-section">
        <div class="description-header">
          <span class="description-label">说明事项：</span>
        </div>
        <div class="description-content">
          <template v-if="isEditing">
            <el-input 
              v-model="formData.description" 
              type="textarea" 
              :rows="4"
              placeholder="请输入说明事项..."
              style="width: 100%;"
            />
          </template>
          <template v-else">
            <div class="description-text">
              {{ formData.description }}
            </div>
          </template>
        </div>
      </div>

      <!-- 复制（提取）地点 -->
      <div class="location-section">
        <div class="location-row">
          <span>复制（提取）地点：</span>
          <template v-if="isEditing">
            <el-input v-model="formData.location" size="small" style="width: 300px;" />
          </template>
          <template v-else">
            <span class="underline-text">{{ formData.location }}</span>
          </template>
        </div>
      </div>

      <!-- 复制（提取）时间 -->
      <div class="time-section">
        <div class="time-row">
          <span>复制（提取）时间：</span>
          <template v-if="isEditing">
            <el-input v-model="formData.year" size="small" style="width: 80px;" />
          </template>
          <template v-else">
            <span class="underline-text">{{ formData.year }}</span>
          </template>
          <span>年</span>
          <template v-if="isEditing">
            <el-input v-model="formData.month" size="small" style="width: 60px;" />
          </template>
          <template v-else">
            <span class="underline-text">{{ formData.month }}</span>
          </template>
          <span>月</span>
          <template v-if="isEditing">
            <el-input v-model="formData.day" size="small" style="width: 60px;" />
          </template>
          <template v-else">
            <span class="underline-text">{{ formData.day }}</span>
          </template>
          <span>日</span>
          <template v-if="isEditing">
            <el-input v-model="formData.hour" size="small" style="width: 60px;" />
          </template>
          <template v-else">
            <span class="underline-text">{{ formData.hour }}</span>
          </template>
          <span>时</span>
          <template v-if="isEditing">
            <el-input v-model="formData.minute" size="small" style="width: 60px;" />
          </template>
          <template v-else">
            <span class="underline-text">{{ formData.minute }}</span>
          </template>
          <span>分</span>
        </div>
      </div>

      <!-- 执法人员及执法证号 -->
      <div class="officer-section">
        <div class="officer-header">
          <span>执法人员及执法证号：</span>
          <div class="officer-controls" v-if="isEditing">
            <el-button size="small" @click="addOfficer">+</el-button>
          </div>
        </div>
        <div class="officer-content">
          <template v-if="isEditing">
            <div 
              v-for="(officer, index) in formData.officers" 
              :key="index"
              class="officer-item"
            >
              <div class="officer-row">
                <span>姓名：</span>
                <el-input v-model="officer.name" size="small" style="width: 120px;" />
                <span style="margin-left: 20px;">执法证号：</span>
                <el-input v-model="officer.license" size="small" style="width: 120px;" />
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="removeOfficer(index)"
                  style="margin-left: 20px;"
                >
                  删除
                </el-button>
              </div>
            </div>
          </template>
          <template v-else">
            <div 
              v-for="(officer, index) in formData.officers" 
              :key="index"
              class="officer-display"
            >
              <span>姓名：</span>
              <span class="underline-text">{{ officer.name }}</span>
              <span style="margin-left: 30px;">执法证号：</span>
              <span class="underline-text">{{ officer.license }}</span>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  bureauName: '惠阳区',
  evidenceItems: [
    { content: '' }
  ],
  description: '',
  location: '',
  year: '2024',
  month: '1',
  day: '15',
  hour: '14',
  minute: '30',
  officers: [
    { name: '王执法', license: 'ZF001' },
    { name: '李执法', license: 'ZF002' }
  ]
})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    formData.value = { ...formData.value, ...newVal }
  }
}, { immediate: true, deep: true })

// 组件挂载时初始化数据
onMounted(() => {
  if (!props.documentData || Object.keys(props.documentData).length === 0) {
    // 可以在这里调用API获取真实数据
  }
})

// 切换编辑状态
const toggleEdit = () => {
  if (isEditing.value) {
    emit('save', formData.value)
    ElMessage.success('保存成功')
  }
  isEditing.value = !isEditing.value
}

// 添加证据项
const addEvidenceItem = () => {
  formData.value.evidenceItems.push({ content: '' })
}

// 删除证据项
const removeEvidenceItem = (index) => {
  if (formData.value.evidenceItems.length > 1) {
    formData.value.evidenceItems.splice(index, 1)
  }
}

// 添加执法人员
const addOfficer = () => {
  formData.value.officers.push({ name: '', license: '' })
}

// 删除执法人员
const removeOfficer = (index) => {
  if (formData.value.officers.length > 1) {
    formData.value.officers.splice(index, 1)
  }
}
</script>

<style scoped>
.document-content {
  padding: 20px;
  max-width: 900px;
  margin: 0 auto;
  background: white;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.document-title {
  text-align: center;
  margin-bottom: 30px;
}

.bureau-name {
  font-size: 16px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.document-title h2 {
  font-size: 24px;
  font-weight: bold;
  margin: 20px 0;
}

.evidence-section {
  margin-bottom: 30px;
}

.evidence-box {
  border: 2px solid #333;
  min-height: 300px;
  position: relative;
}

.evidence-header {
  position: absolute;
  top: -12px;
  left: 20px;
  background: white;
  padding: 0 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.evidence-label {
  font-size: 14px;
  color: #666;
}

.evidence-content {
  padding: 30px 20px 20px 20px;
  min-height: 250px;
}

.evidence-item {
  margin-bottom: 20px;
}

.evidence-display {
  margin-bottom: 15px;
  line-height: 1.8;
  min-height: 50px;
  padding: 10px;
  border: 1px dashed #ddd;
  background-color: #fafafa;
}

.description-section {
  margin-bottom: 30px;
}

.description-header {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.description-label {
  font-weight: 500;
  color: #333;
}

.description-content {
  border: 1px solid #ddd;
  min-height: 100px;
  padding: 15px;
}

.description-text {
  line-height: 1.8;
  min-height: 70px;
}

.location-section {
  margin-bottom: 20px;
}

.location-row {
  display: flex;
  align-items: center;
  line-height: 2;
}

.time-section {
  margin-bottom: 30px;
}

.time-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 5px;
  line-height: 2;
}

.officer-section {
  margin-bottom: 30px;
}

.officer-header {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.officer-content {
  border: 1px solid #ddd;
  padding: 15px;
  min-height: 80px;
}

.officer-item {
  margin-bottom: 15px;
}

.officer-row {
  display: flex;
  align-items: center;
  line-height: 2;
}

.officer-display {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  line-height: 2;
}

.officer-controls {
  position: absolute;
  top: -12px;
  right: 20px;
  background: white;
  padding: 0 10px;
}

.evidence-controls {
  background: white;
  padding: 0 5px;
}

.underline-text {
  border-bottom: 1px solid #333;
  min-width: 60px;
  padding: 2px 5px;
  margin: 0 3px;
  display: inline-block;
  text-align: center;
}

/* 打印样式 */
@media print {
  .section-header {
    display: none;
  }
  
  .document-content {
    padding: 0;
    box-shadow: none;
  }
  
  .evidence-controls,
  .officer-controls {
    display: none;
  }
}
</style>
