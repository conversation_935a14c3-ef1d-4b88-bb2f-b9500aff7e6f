package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springblade.modules.hzyc.DocumentGeneration.service.IMcGatewayHeaderService;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@AllArgsConstructor
public class CaseInfoServiceImpl implements ICaseInfoService {

    private final IMcGatewayHeaderService mcGatewayHeaderService;

    /**
     * 零售案件基本信息
     */


    @Override
    public JSONArray getCaseBasicInfo(Map<String, Object> query) {
        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        requestBody.set("query", query);
        String queryStr = JSONUtil.toJsonStr(requestBody);

        // 调用网关服务
        String serviceId = "sv242250IAV7"; // 根据实际接口配置
        String responseStr = mcGatewayHeaderService.executeRequest(serviceId, queryStr);

        if (responseStr == null) {
            return new JSONArray();
        }

        // 解析响应
        JSONObject response = JSONUtil.parseObj(responseStr);
        Integer errcode = response.getInt("errcode");

        if (errcode != null && errcode == 0) {
            JSONObject data = response.getJSONObject("data");
            if (data != null) {
                return data.getJSONArray("data");
            }
        }

        return new JSONArray();
    }

	@Override
	public JSONArray getAPIData(Map<String, Object> query, String serviceId) {
		// 构建请求参数
		JSONObject requestBody = new JSONObject();
		requestBody.set("query", query);
		String queryStr = JSONUtil.toJsonStr(requestBody);

		// 调用网关服务
		String responseStr = mcGatewayHeaderService.executeRequest(serviceId, queryStr);

		if (responseStr == null) {
			return new JSONArray();
		}

		// 解析响应
		JSONObject response = JSONUtil.parseObj(responseStr);
		Integer errcode = response.getInt("errcode");

		if (errcode != null && errcode == 0) {
			JSONObject data = response.getJSONObject("data");
			if (data != null) {
				return data.getJSONArray("data");
			}
		}

		return new JSONArray();
	}
    /**
     * 惠州案件抽样取证物品清单信息
     */

    @Override
    public JSONArray getCaseSamplingItemList(Map<String, Object> query) {
		String originSampleData = """
			{
			  "data": [
			    {
			      "CYQDMXBS": "ebf2cfc13dec46e8b76a8377c232d1df",
			      "AJMC": "王**涉嫌无烟草专卖零售许可证经营烟草制品零售业务",
			      "DSR": "王**",
			      "AY": {},
			      "KZZD1": {},
			      "ND": {},
			      "CYQZWPQDBS": "e978c62876ca42b29bf9d2a41f13b604",
			      "TZZD": {},
			      "AJBS": "003b019c8db844c0bf53560e7a81e69d",
			      "XTCJSJCXBYDX": [2025, 5, 12, 10, 38, 1],
			      "FWZXZDTBSJYFWZXSJS": {},
			      "BZXS": "条盒硬盒",
			      "SJBM": "10441300",
			      "WSRQ": 1746979200000,
			      "XGR": "郑**",
			      "XGSJ": [2025, 5, 12, 10, 38, 1],
			      "CYNR": {},
			      "KZZD3": {},
			      "WPUUID": "001369",
			      "SXXZFLDL": "02",
			      "WPFL": "11",
			      "CYRQ": 1746979200000,
			      "WSHQ": {},
			      "MCRKSJ": [2025, 8, 7, 3, 13, 33],
			      "KZZD2": {},
			      "XYWYBS": "",
			      "SXXZFLXL": "03",
			      "CLJG": {},
			      "WPMXBS": "f0a40fc1e36142868aa4965e184179ae",
			      "WSH": {},
			      "SJMC": "惠州市",
			      "XTGXSJCXBYDX": [2025, 5, 12, 12, 41, 24],
			      "XH": 7,
			      "GG": "84",
			      "BZ": "卷烟真假待鉴定。",
			      "FWZXZDTBSJYFWZXSJG": {},
			      "CBRUUID": "4413231223000010566,4413231223000010559",
			      "DW": "条",
			      "LABH": "博烟立﹝2025﹞第65号",
			      "SFYX": 0,
			      "SJSSBMYYTYSJQXCL": "4413240703000000170",
			      "YPJS": "1.000000000000000000",
			      "CBR": "李**(190****2017),郑**(190****2029)",
			      "TAR": {},
			      "CYSL": "1.000000000000000000",
			      "DWJC": "广东省博罗县烟草专卖局",
			      "CYDD": "广东省惠州市博罗县园洲镇上南村兴园五路172号（自主承诺申报）",
			      "CJR": "郑**",
			      "DWSXZ": {},
			      "WPMC": "双喜(硬经典1906)",
			      "CBRZFZH": {},
			      "SJSSDWYYTYSJQXCL": "4413231030000000540",
			      "WSZT": {},
			      "CYMD": "鉴别检验",
			      "WFGD": {},
			      "WSCJSJ": [2025, 5, 12, 10, 38, 1]
			    }
			  ]
			}

			""";
        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        requestBody.set("query", query);
        String queryStr = JSONUtil.toJsonStr(requestBody);

        // 调用网关服务
        String serviceId = "sv25213NFF00"; // 根据实际接口配置
        String responseStr = mcGatewayHeaderService.executeRequest(serviceId, queryStr);

        if (responseStr == null) {
            return new JSONArray();
        }

        // 解析响应
        JSONObject response = JSONUtil.parseObj(responseStr);
        Integer errcode = response.getInt("errcode");

        if (errcode != null && errcode == 0) {
            JSONObject data = response.getJSONObject("data");
            if (data != null) {
                return data.getJSONArray("data");
            }
        }

        return new JSONArray();
    }

    @Override
    public JSONArray getCaseHandleApprovalDailyReport(Map<String, Object> query) {
		String originSampleData = """
			{
			  "JGSXZ": "龙门",
			  "CBRYJ": "本案当事人梁**对其违法经营卷烟的事实供认不讳，本案事实清楚、证据确凿，结合《广东省惠州市烟草专卖局行政处罚自由裁量权实施办法》规定，根据《中华人民共和国烟草专卖法实施条例》第五十六条的规定，拟对当事人作出如下行政处罚：\\n处以当事人梁**未在当地烟草专卖批发企业进货，进货总额人民币3310.00元6%的罚款，即罚款人民币198.6元。",
			  "AJBS": "008a2ca3592c4f8688a696b16736003a",
			  "CJR": "李**",
			  "SFLX": {},
			  "AJBH": "4413240202310059",
			  "JGJC": "广东省龙门县烟草专卖局",
			  "CBRUUID": "4413231223000010665,4413231223000010672",
			  "KZZD2": {},
			  "DSRXM": {},
			  "CLSPBS": "6c35e199282b4e8a9eaac71c0907d47d",
			  "LARQ": 1695657600000,
			  "ZZ": {},
			  "LABH": "龙门烟立[2023]第59号",
			  "CHBMUUID": "*******************",
			  "QYMC": {},
			  "CBBMUUID": "4413240703000000161",
			  "FZRYJ": {},
			  "SJMC": "惠州市",
			  "AJSS": "    2023年09月26日 10时25分，我局稽查大队进行市场例行检查，我局执法人员李**(190****2007),陈**(190****2003)经出示证件、表明身份后,依法对梁**位于广东省惠州市龙门县平陵街道易发大道**号的经营场所平陵镇常俊商店进行检查，并告知当事人依法享有陈述和申辩的权利，我局执法人员在其广东省惠州市龙门县平陵街道易发大道**号的经营场所查获涉嫌违法经营卷烟有：芙蓉王(硬)7条、芙蓉王(蓝)3条、双喜(硬经典1906)3条；以上3个品牌规格合计13条。当事人在现场，现场也无法提供上述卷烟合法有效的来源证明。经调查了解，当事人梁**持有烟草专卖零售许可证，许可证的经营地址为广东省惠州市龙门县平陵街道易发大道**号，许可证号为4405*********。当事人梁**涉嫌违反《中华人民共和国烟草专卖法实施条例》第二十三条第二款，经报局领导批准同意，我局执法人员依法对上述涉嫌违法卷烟采取先行登记保存措施。\\n    2023年09月26日至2023年10月25日，我局对该案进行了调查取证，上述涉案卷烟为当事人梁**所有，当事人已在广东省龙门县烟草专卖局领取烟草专卖零售许可证（4405*********）。上述涉案卷烟是当事人梁**在上门推销烟贩处购进的，购进后放在商店里销售，尚未销售即被查获。当事人梁**无法提供购进涉案卷烟的合法有效证明。经对这3个品牌规格的涉嫌违法卷烟各进行抽样1条送广东省烟草质量监督检测站鉴定，芙蓉王(硬)7条、芙蓉王(蓝)3条、双喜(硬经典1906)3条，3个品牌规格的13条卷烟均为国产真品卷烟。经广东省惠州市烟草专卖局（公司）涉案烟草制品价格管理小组，3个品牌规格合计13条卷烟的估算总价格为3310.00。经查，梁**一年内未因违法经营被我局处以行政处罚，梁**未因违法生产经营卷烟、雪茄烟被烟草专卖局或者其他执法机关处罚过。\\n    以上事实有以下证据予以证实：1、涉案卷烟共3个品牌规格合计13条；2、《证据先行登记保存通知书》1份；3、证据复制（提取）单6份（含提取当事人梁**身份证、烟草专卖零售许可证复印件各1份、涉案店铺照片1张、涉案卷烟照片2张、查处非烟（32位）条码登记表1份）；4、《询问笔录》1份；5、《检查（勘验）笔录》1份；6、《卷烟、雪茄烟鉴别检验报告》1份； 7、《涉案烟草专卖品核价表》1份。",
			  "DSRXB": {},
			  "XGR": "李**",
			  "FZFZR": {},
			  "BMFZR": {},
			  "FZR": {},
			  "ZZJGUUID": "4413231030000010543",
			  "FDDBR": {},
			  "AJLY": "02",
			  "BZ": "",
			  "KZZD1": {},
			  "SFYX": 1,
			  "ZY": {},
			  "CJSJ": [
			    2023,
			    12,
			    25,
			    10,
			    37,
			    44
			  ],
			  "FZSHSJ": {},
			  "KZZD3": {},
			  "SJBM": "10441300",
			  "CFYJ": "《中华人民共和国烟草专卖法实施条例》第五十六条",
			  "ZMBMYJSJ": {},
			  "JYDZ": {},
			  "QYLXDH": {},
			  "AY": "未在当地烟草专卖批发企业进货",
			  "XGSJ": [
			    2024,
			    7,
			    20,
			    2,
			    3,
			    11
			  ],
			  "TJSJ": 1700409600000,
			  "BMSHSJ": {},
			  "XTGXSJ": [
			    2023,
			    12,
			    25,
			    10,
			    37,
			    44
			  ],
			  "FZSHYJ": {},
			  "TAR": {},
			  "ZJHM": {},
			  "ZMBMFZR": {},
			  "ZMBMYJ": {},
			  "NL": {},
			  "FWZXSJGXSJ": {},
			  "SFYS": {},
			  "CBR": "谢**(190****2004),李**(190****2007)",
			  "SJSSBM": "4413240703000000161",
			  "FZRSPSJ": {},
			  "XTCJSJ": [
			    2023,
			    12,
			    25,
			    10,
			    37,
			    44
			  ],
			  "FWZXSJSCBJ": {},
			  "XYWYBS": "",
			  "MCRKSJ": [
			    2025,
			    8,
			    7,
			    3,
			    14,
			    34
			  ],
			  "AJMC": "梁**涉嫌未在当地烟草专卖批发企业进货",
			  "SJSSDW": "4413231030000010543",
			  "CSRQ": {},
			  "SFZSFJMS": {},
			  "ZW": {},
			  "WCZT": {},
			  "CBBMYJ": {},
			  "MZ": {},
			  "TART": "无",
			  "ZJLX": {},
			  "LXDH": {}
			}
			""";
		String sampleData = "is_branch=null, reg_no=龙门烟立[2023]第59号, contact_phone=null, handle_person_uuids=4413231223000010665,4413231223000010672, nation=null, modifier=李成江, pic=null, dept_audit_date=null, tid=, get_dept_uuid=*******************, r_dept_date=null, party_sex=null, ext3=null, case_partner=null, ext2=null, r_dept_manager=null, ext1=null, busi_address=null, create_time=[2023,12,25,10,37,44], submit_time=1700409600000, city_org_code=10441300, mc_tec_ctime=[2025,8,6,3,14,47], dept_manager=null, com_phone=null, is_trans=null, reg_date=1695657600000, is_type=null, apv_pic_date=null, org_shortname=广东省龙门县烟草专卖局, case_source_clues=02, company_name=null, id_card_type=null, party=null, duties=null, birthday=null, handle_person=谢楠(19090552004),李成江(19090552007), dept_advice=null, city_org_name=惠州市, modify_time=[2024,7,20,2,3,11], case_name=梁文斌涉嫌未在当地烟草专卖批发企业进货, id_card=null, remark=, legal_manager=null, handle_person_advice=本案当事人梁文斌对其违法经营卷烟的事实供认不讳，本案事实清楚、证据确凿，结合《广东省惠州市烟草专卖局行政处罚自由裁量权实施办法》规定，根据《中华人民共和国烟草专卖法实施条例》第五十六条的规定，拟对当事人作出如下行政处罚：\n" +
			"处以当事人梁文斌未在当地烟草专卖批发企业进货，进货总额人民币3310.00元6%的罚款，即罚款人民币198.6元。, same_party=无, cause_of_action=未在当地烟草专卖批发企业进货, r_dept_advice=null, sysupdatetime=null, punish_argument=《中华人民共和国烟草专卖法实施条例》第五十六条, creator=李成江, case_fact=    2023年09月26日 10时25分，我局稽查大队进行市场例行检查，我局执法人员李成江(19090552007),陈远航(19090552003)经出示证件、表明身份后,依法对梁文斌位于广东省惠州市龙门县平陵街道易发大道21号的经营场所平陵镇常俊商店进行检查，并告知当事人依法享有陈述和申辩的权利，我局执法人员在其广东省惠州市龙门县平陵街道易发大道21号的经营场所查获涉嫌违法经营卷烟有：芙蓉王(硬)7条、芙蓉王(蓝)3条、双喜(硬经典1906)3条；以上3个品牌规格合计13条。当事人在现场，现场也无法提供上述卷烟合法有效的来源证明。经调查了解，当事人梁文斌持有烟草专卖零售许可证，许可证的经营地址为广东省惠州市龙门县平陵街道易发大道21号，许可证号为440522800015。当事人梁文斌涉嫌违反《中华人民共和国烟草专卖法实施条例》第二十三条第二款，经报局领导批准同意，我局执法人员依法对上述涉嫌违法卷烟采取先行登记保存措施。\n" +
			"    2023年09月26日至2023年10月25日，我局对该案进行了调查取证，上述涉案卷烟为当事人梁文斌所有，当事人已在广东省龙门县烟草专卖局领取烟草专卖零售许可证（440522800015）。上述涉案卷烟是当事人梁文斌在上门推销烟贩处购进的，购进后放在商店里销售，尚未销售即被查获。当事人梁文斌无法提供购进涉案卷烟的合法有效证明。经对这3个品牌规格的涉嫌违法卷烟各进行抽样1条送广东省烟草质量监督检测站鉴定，芙蓉王(硬)7条、芙蓉王(蓝)3条、双喜(硬经典1906)3条，3个品牌规格的13条卷烟均为国产真品卷烟。经广东省惠州市烟草专卖局（公司）涉案烟草制品价格管理小组，3个品牌规格合计13条卷烟的估算总价格为3310.00。经查，梁文斌一年内未因违法经营被我局处以行政处罚，梁文斌未因违法生产经营卷烟、雪茄烟被烟草专卖局或者其他执法机关处罚过。\n" +
			"    以上事实有以下证据予以证实：1、涉案卷烟共3个品牌规格合计13条；2、《证据先行登记保存通知书》1份；3、证据复制（提取）单6份（含提取当事人梁文斌身份证、烟草专卖零售许可证复印件各1份、涉案店铺照片1张、涉案卷烟照片2张、查处非烟（32位）条码登记表1份）；4、《询问笔录》1份；5、《检查（勘验）笔录》1份；6、《卷烟、雪茄烟鉴别检验报告》1份； 7、《涉案烟草专卖品核价表》1份。, is_active=1, sys_modify_time=[2023,12,25,10,37,44], case_code=4413240202310059, own_dept_uuid=4413240703000000161, own_org_uuid=4413231030000010543, finsh_status=null, home_addr=null, reg_dept_uuid=4413240703000000161, case_uuid=008a2ca3592c4f8688a696b16736003a, legal_advice=null, org_uuid=4413231030000010543, vocation=null, manager_name=null, handle_uuid=6c35e199282b4e8a9eaac71c0907d47d, apv_pic_advice=null, org_abbr=龙门, sysisdelete=null, legal_audit_date=null, age=null, sys_create_time=[2023,12,25,10,37,44]";
        return executeGatewayRequest("sv252132XJUB", query);
    }

    @Override
    public JSONArray getCaseInvestigationEndDailyReport(Map<String, Object> query) {
		String originSampleData = """
			{
			  "SJMC": "惠州市",
			  "XGR": "郑**",
			  "CBBMUUID": "4413240703000000170",
			  "JGJC": "广东省博罗县烟草专卖局",
			  "TAR": {},
			  "LXDH": {},
			  "XYWYBS": "",
			  "CBR": "李**(190****2017),郑**(190****2029)",
			  "ZJLX": {},
			  "JGSXZ": "博",
			  "WCZT": {},
			  "ZZJGUUID": "4413231030000000540",
			  "SJSSBMYYTYSJQXCL": "4413240703000000170",
			  "XB": {},
			  "CHBMUUID": "4413240703000000170",
			  "XTGXSJCXBYDX": [2025, 5, 15, 8, 52, 4],
			  "AJXZ": "无烟草专卖零售许可证经营烟草制品零售业务",
			  "AJLY": "02",
			  "CLYJ": " 当事人的行为违反了《中华人民共和国烟草专卖法》第十六条的规定，涉嫌构成无烟草专卖零售许可证经营烟草制品零售业务的违法行为。依据《中华人民共和国烟草专卖法》第三十二条和《烟草专卖行政处罚程序规定》第十三条第一款的规定，建议将本案依法移交博罗县市场监督管理局处理。",
			  "WFFLTK": "《中华人民共和国烟草专卖法》第十六条、《中华人民共和国烟草专卖法实施条例》第六条",
			  "AJBH": "4413220202510065",
			  "LARQ": 1746979200000,
			  "AYUUIDS": {},
			  "SJSSDWYYTYSJQXCL": "4413231030000000540",
			  "MCRKSJ": [2025, 8, 7, 3, 13, 32],
			  "JGJC_": "广东省博罗县烟草专卖局",
			  "DCZJRQ": {},
			  "AY": "无烟草专卖零售许可证经营烟草制品零售业务",
			  "KZZD2": {},
			  "FWZXZDTBSJYFWZXSJS": {},
			  "CJR": "郑**",
			  "XGSJ": [2025, 5, 15, 8, 52, 4],
			  "FWZXZDTBSJYFWZXSJG": {},
			  "FDDBR": {},
			  "DZ": {},
			  "ZJHM": {},
			  "CJSJ": [2025, 5, 15, 8, 52, 4],
			  "SJBM": "10441300",
			  "XTCJSJCXBYDX": [2025, 5, 15, 8, 52, 4],
			  "SFYX": 1,
			  "CFYJ": "《中华人民共和国烟草专卖法》第三十二条、《烟草专卖行政处罚程序规定》第十三条第一款",
			  "CSRQ": {},
			  "AJXZID": "14",
			  "KZZD3": {},
			  "AJMC": "王**涉嫌无烟草专卖零售许可证经营烟草制品零售业务",
			  "DSR": {},
			  "KZZD1": {},
			  "SFWZ": {},
			  "BGRQ": 1747152000000,
			  "AJBS": "003b019c8db844c0bf53560e7a81e69d",
			  "BZ": "/",
			  "DCSS": "    2025年05月12日09时25分，本局专卖执法人员经出示执法证件，表明身份，说明来意后，依法对位于广东省惠州市博罗县园洲镇上南村兴园五路172号（自主承诺申报）的博罗县园洲镇万田便利店（实际店铺招牌名称：中业爱民，烟草专卖零售许可证：无）进行检查，在该场所内发现涉嫌违法的烟草专卖品七匹狼(纯境)2条、钻石(荷花)2条、双喜(硬经典1906)1条、芙蓉王(硬)1条、真龙(起源)1条、玉溪(软)1条、贵烟(跨越)1条、南京(炫赫门)1条、利群(新版)1条，合计9个品种11条（疑似非法生产的卷烟）。上述烟草专卖品条包装上均无当地烟草专卖批发企业喷码。经现场询问被检查人王**，其无法提供上述卷烟从当地烟草专卖批发企业进货的发票或其他合法有效凭证。因当事人的行为涉嫌违反《中华人民共和国烟草专卖法》第十六条、《中华人民共和国烟草专卖法实施条例》第六条的规定，有无烟草专卖零售许可证经营烟草制品零售业务的嫌疑。经本局负责人批准，专卖执法人员即将上述涉案物品作为证据，依法予以先行登记保存。整个检查过程，被检查人始终在场，并未对该先行登记保存措施提出陈述与申辩。\\n    经查明，当事人王**未取得烟草专卖零售许可资格，为谋取不法利益，从一个上门兜售卷烟的烟贩处购进11条卷烟，放在位于广东省惠州市博罗县园洲镇上南村兴园五路172号（自主承诺申报）的“博罗县园洲镇万田便利店”进行销售，被我局依法查获。根据广东省惠州市烟草专卖局（公司）涉案烟草制品价格管理小组的核价意见：全部涉案卷烟共计9个品牌11条的货值总额为人民币2430.00元。另查明当事人王**没有因涉烟违法行为被处罚过。当事人王**的行为违反了《中华人民共和国烟草专卖法》第十六条的规定,涉嫌构成无烟草专卖零售许可证经营烟草制品零售业务的违法行为。\\n   以上事实有以下证据予以证实：1、涉案卷烟共9个品牌规格合计11条；2、《证据先行登记保存通知书》1份；3、证据复制（提取）单（含提取当事人王**身份证、营业执照复印件各1份，涉案店铺、涉案卷烟、被我局先行登记保存的卷烟照片各1张）；4、《询问笔录》1份；5、《现场笔录》1份；6、《涉案物品核价表》1份。",
			  "ZJBGBS": "60d9ac42e4ff45f5a6c3ff99cd234082",
			  "CBRUUIDS": "4413231223000010566,4413231223000010559"
			}

			""";
        return executeGatewayRequest("sv25213UF1AB", query);
    }

    @Override
    public JSONArray getCaseInquiryRecordDailyReport(Map<String, Object> query) {
		String originSampleData = """
			[{
			  "BXWRXB": "01",
			  "XWSY": {},
			  "BMSFJL": "我们是广东省博罗县烟草专卖局的行政执法人员李*、郑*元，这是我们的执法证件，执法证号分别是*************, *************。",
			  "BXWRDW": {},
			  "WSCJSJ": [2025, 5, 12, 10, 31, 17],
			  "WSHQ": "王*田",
			  "BXWRNL": "47",
			  "WSZT": {},
			  "BXWRLXDH": "180****3898",
			  "HD2": {},
			  "XWRUUID": "4413231223000010566,4413231223000010559",
			  "BXWRJYDZ": "广东省惠州市博罗县园洲镇…**…（自主承诺申报）",
			  "SFYYD0WYD1YYD": 0,
			  "SJSSBMYYTYSJQXCL": "4413240703000000170",
			  "BXWRCSRQ": {},
			  "KZZD1": {},
			  "SFYX": 1,
			  "HD1": {},
			  "MCRKSJ": [2025, 8, 7, 3, 15, 16],
			  "XGR": "郑*元",
			  "CHBMUUID": "4413240703000000170",
			  "XWBLBS": "6152662ea7de4bed9778356adea14087",
			  "CBBMUUID": "4413240703000000170",
			  "BXWRZJLX": "01",
			  "AJMC": "王*田涉嫌无烟草专卖零售许可证经营烟草制品零售业务",
			  "AJBS": "003b019c8db844c0bf53560e7a81e69d",
			  "FWZXZDTBSJYFWZXSJG": {},
			  "TAR": {},
			  "SJMC": "惠州市",
			  "FWZXZDTBSJYFWZXSJS": {},
			  "XWR": "李*(*************), 郑*元(*************)",
			  "ZZJGUUID": "4413231030000000540",
			  "GZCS": "根据《中华人民共和国行政处罚法》...",
			  "XTGXSJCXBYDX": [2025, 5, 12, 10, 31, 17],
			  "XWRZFZH": {},
			  "XWRSZZMJ": "广东省博罗县烟草专卖局",
			  "BXWRHM": "352229********6531",
			  "XWDD": "广东省惠州市博罗县园洲镇…**…（自主承诺申报）",
			  "FDDBRFZR": {},
			  "XGSJ": [2025, 5, 12, 10, 31, 17],
			  "BXWR": "王*田",
			  "SJSSDWYYTYSJQXCL": "4413231030000000540",
			  "JGSXZ": "博",
			  "CJR": "郑*元",
			  "SJBM": "10441300",
			  "XWSJZ": 1746979200000,
			  "XYWYBS": "",
			  "JGJC": "广东省博罗县烟草专卖局",
			  "AJBH": "4413220202510065",
			  "DSR": "王*田",
			  "XTCJSJCXBYDX": [2025, 5, 12, 10, 31, 17],
			  "BZ": "",
			  "XWNR": "问：说说你的姓名、年龄、性别、籍贯、住址、身份证号码、经营地址？\\n答：我叫王*田，今年47岁，男性，籍贯是福建省泉州市，户籍地址是福建省泉州市洛江区虹山乡…**…，身份证号码是352229********6531，经营地点是广东省惠州市博罗县园洲镇…**…。\\n问：你从事什么职业？联系电话多少？\\n答：我开了一家便利商店，卖一些日杂百货和卷烟，我的联系电话是180****3898。\\n...\\n问：这个烟贩子叫什么名字？\\n答：他自称“小王”...\\n"
			}]
				""";
        return executeGatewayRequest("sv252131HZDZ", query);
//        return JSONUtil.parseArray(originSampleData);
    }

    @Override
    public JSONArray getCaseEvidenceCopyDailyReport(Map<String, Object> query) {
		String originSampleData = """
			[{
			  "TQDD": "广东省惠州市龙门县龙城街道（自主申报）",
			  "ZFRYJZFZH": "蒋**(190****2017),廖**(190****2011)",
			  "DSR": {},
			  "CJR": "蒋**",
			  "WSHZJLX": "现场照片",
			  "AJBS": {},
			  "CBBMUUID": {},
			  "XGSJ": [2024, 7, 20, 2, 3, 33],
			  "FWZXZDTBSJYFWZXSJG": {},
			  "TGR": {},
			  "SMSX": "以上提取的是我局执法人员在广东省惠州市龙门县龙城街道（自主申报）龙门县泰洲商行进行执法检查时的现场执法照片,由蒋**(190****2017),廖**(190****2011)采集。",
			  "XYWYBS": "",
			  "ZJFZBS": "93579a9c1cbc430dbd501fcaf4e8fd46",
			  "XTCJSJCXBYDX": [2023, 9, 26, 10, 26, 36],
			  "KZZD1": {},
			  "SJSSDWYYTYSJQXCL": "4413231030000010543",
			  "TAR": {},
			  "TZZD": {},
			  "SJMC": "惠州市",
			  "SFYX": 0,
			  "DWJC": "广东省龙门县烟草专卖局",
			  "MCRKSJ": [2025, 8, 7, 3, 13, 32],
			  "KZZD2": {},
			  "TGRDW": "杨**",
			  "ZJLX": "01",
			  "XGR": "蒋**",
			  "JGSXZ": {},
			  "WSSJ": 1695657600000,
			  "XTGXSJCXBYDX": [2023, 11, 28, 9, 36, 33],
			  "BZ": "",
			  "CJSJ": [2023, 9, 26, 10, 26, 36],
			  "SJBM": "10441300",
			  "AJMC": {},
			  "ZZJGUUID": {},
			  "KZZD3": {},
			  "SJSSBMYYTYSJQXCL": "4413240703000000161",
			  "CHBMUUID": {},
			  "AJBH": {},
			  "FWZXZDTBSJYFWZXSJS": {},
			  "TQSJ": 1695657600000,
			  "ZFRYUUIDS": "4413231223000010669,4413231223000010670"
			}]

			""";
//        return executeGatewayRequest("sv25213J21ZV", query);
        return JSONUtil.parseArray(originSampleData);
    }

    @Override
    public JSONArray getCaseItemInfoDailyReport(Map<String, Object> query) {
        return executeGatewayRequest("sv25213MJLFI", query);
    }

    @Override
    public JSONArray getAdminPenaltyDecisionDailyReport(Map<String, Object> query) {
		String originSampleData = """
			[{
			  "FLTK": "《中华人民共和国烟草专卖法实施条例》第二十三条第二款",
			  "DSR_NAME": "陈*嫦",
			  "XKZH": "",
			  "AJXZ": "未在当地烟草专卖批发企业进货",
			  "JKDH": {},
			  "WFCD": {},
			  "XGSX": {},
			  "KZZD1": {},
			  "ZJNR": {},
			  "TYZDTS": {},
			  "XZFYDW": {},
			  "AJMC": "陈*嫦 涉嫌未在当地烟草专卖批发企业进货",
			  "SY": {},
			  "SZSJBM": "10441300",
			  "GXSJ": [2024, 7, 30, 17, 39, 26],
			  "DSRID": {},
			  "SJSSDW": "4413231030000010540",
			  "WSHQ": "惠东烟处[2017]第183号",
			  "FBSJ": {},
			  "XYWYBS": {},
			  "XTGXSJ": [2017, 10, 9, 13, 56, 46],
			  "FWZXSJGXSJ": "2017-10-09 13:56:46",
			  "CBBMUUID": "4413240703000000159",
			  "CJSJ": [2017, 10, 9, 13, 56, 46],
			  "JDSBS": "8a83cd905ec6da3a015ecb5506393144",
			  "DWJC": "广东省惠东县烟草专卖局",
			  "SSFY": {},
			  "AFDD": "广东省惠东县多祝镇新华南路**号",
			  "SFJQCF": {},
			  "ZFRY": {},
			  "MSK": {},
			  "SGJE": {},
			  "AFRQ": 1495123200000,
			  "GGRQ": {},
			  "SFYX": 1,
			  "DSRJBXX": {},
			  "SFDX": 0,
			  "AJBS": "8a83cd905e5592df015e55fd6df74cde",
			  "ZZJGUUID": "4413231030000010540",
			  "XGSJ": [2024, 7, 20, 2, 5, 14],
			  "XTCJSJ": [2017, 10, 9, 13, 56, 46],
			  "JKJG": {},
			  "CFJD": "处以当事人未在当地烟草专卖批发企业进货总额人民币390.00元5％的罚款，即罚款人民币19.50元。",
			  "RKSJ": [2024, 7, 30, 17, 39, 42],
			  "ZLBS": "I",
			  "DSR": "陈*嫦",
			  "JGSXZ": "惠东",
			  "KZZD3": {},
			  "JKDH_INC": 976,
			  "CFZL": {},
			  "CJR": {},
			  "JDSLX": "1",
			  "XGR": {},
			  "TAR": "",
			  "WFSS": {},
			  "FWZXSJSCBJ": 0,
			  "LASJ": 1495123200000,
			  "FKJE": "19.500000000000000000",
			  "SJSSBM": "4413240703000000159",
			  "MCRKSJ": [2025, 8, 7, 1, 23],
			  "AJBH": "44132320171069",
			  "CHBMUUID": "4413240703000000159",
			  "JKSJ": {},
			  "SZSJMC": "惠州市",
			  "KZZD2": {},
			  "FBZT": {},
			  "WSRQ": 1498406400000,
			  "FBLJ": {},
			  "JGJC": "广东省惠东县烟草专卖局"
			}]

			""";
        return executeGatewayRequest("sv25213QDYK3", query);
//        return JSONUtil.parseArray(originSampleData);
    }

    @Override
    public JSONArray getAdminPenaltyNoticeDailyReport(Map<String, Object> query) {
		String originSampleData = """
			{
			  "AJBH": "4413240202310059",
			  "GXSJ": [2024, 7, 30, 17, 41, 36],
			  "XTGXSJCXBYDX": [2023, 12, 25, 10, 46, 35],
			  "WSH": "59",
			  "AJMC": "梁**涉嫌未在当地烟草专卖批发企业进货",
			  "CFYJ": "《中华人民共和国烟草专卖法实施条例》第五十六条",
			  "CHBMUUID": "*******************",
			  "WFSS": {},
			  "SXGZSBS": "16926902c459428abe0a91601ea8e26c",
			  "MCRKSJ": [2025, 8, 7, 3, 13, 25],
			  "DSRYJ": {},
			  "SJBM": "10441300",
			  "DWJC": "广东省龙门县烟草专卖局",
			  "WFXWCD": {},
			  "SJSSDWYYTYSJQXCL": "4413231030000010543",
			  "CJSJ": [2023, 12, 25, 10, 46, 35],
			  "XGSJ": [2024, 7, 19, 21, 30, 15],
			  "ZFBMDH": "0752-7781678",
			  "SDFS": {},
			  "DWDZ": {},
			  "BZ": "",
			  "AJXZ": "未在当地烟草专卖批发企业进货",
			  "XGR": "谢**",
			  "DWDHLXHM": {},
			  "ZLBS": "I",
			  "DWSXZ": "龙门",
			  "KZZD1": {},
			  "AJBS": "008a2ca3592c4f8688a696b16736003a",
			  "SFYX": 1,
			  "ZZJGUUID": "4413231030000010543",
			  "ND": "2023",
			  "YB": "516800",
			  "RKSJ": [2024, 7, 30, 17, 41, 36],
			  "FWZXZDTBSJYFWZXSJS": {},
			  "WFTK": "《中华人民共和国烟草专卖法实施条例》第二十三条第二款",
			  "ZJNR": {},
			  "WSHQ": "龙门烟处告﹝2023﹞第59号",
			  "KZZD2": {},
			  "FWZXZDTBSJYFWZXSJG": {},
			  "SJSSBMYYTYSJQXCL": "*******************",
			  "SJMC": "惠州市",
			  "SDDD": {},
			  "CJR": "谢**",
			  "LXR": {},
			  "XYWYBS": "",
			  "CSSBQX": {},
			  "AFSJ": 1695657600000,
			  "TAR": {},
			  "KZZD3": {},
			  "CFXX": "处以当事人梁**未在当地烟草专卖批发企业进货，进货总额人民币3310.00元6%的罚款，即罚款人民币198.6元。",
			  "ZFBM": "龙门县西林路***号 ",
			  "WSRQ": 1701878400000,
			  "CBBMUUID": "4413240703000000161",
			  "CBRUUIDS": "4413231223000010665,4413231223000010672",
			  "DSR": "梁**",
			  "CBR": "谢**(190****2004),李**(190****2007)",
			  "SFLX0F1S": {},
			  "XTCJSJCXBYDX": [2023, 12, 25, 10, 46, 35]
			}
			""";
		String sampleData = "{excuse_limit_day=null, modifier=谢楠, legal_argument=《中华人民共和国烟草专卖法实施条例》第二十三条第二款, tid=, get_dept_uuid=*******************, ext3=null, ext2=null, org_addr_street=null, ext1=null, create_time=[2023,12,25,10,46,35], city_org_code=10441300, mc_tec_ctime=[2025,8,6,3,13,9], dc_tec_utime=[2024,7,30,17,41,36], org_phone_no=null, undertaker=谢楠(19090552004),李成江(19090552007), bcb_enum_legal_level=null, legal_fact=null, full_doc_no=龙门烟处告﹝2023﹞第59号, org_shortname=广东省龙门县烟草专卖局, org_addr=龙门县西林路218号 , dc_tec_ctime=[2024,7,30,17,41,36], party=梁文斌, party_advice=null, evide_content=null, city_org_name=惠州市, doc_no=59, case_name=梁文斌涉嫌未在当地烟草专卖批发企业进货, modify_time=[2024,7,19,21,30,15], doc_year=2023, remark=, service_addr=null, same_party=null, cause_of_action=未在当地烟草专卖批发企业进货, sysupdatetime=null, punish_argument=《中华人民共和国烟草专卖法实施条例》第五十六条, dc_tec_operation=I, undertaker_uuids=4413231223000010665,4413231223000010672, punish_notice_uuid=16926902c459428abe0a91601ea8e26c, service_style=null, creator=谢楠, sys_modify_time=[2023,12,25,10,46,35], is_active=1, case_code=4413240202310059, own_dept_uuid=*******************, contact_person=null, own_org_uuid=4413231030000010543, finsh_status=null, reg_dept_uuid=4413240703000000161, case_uuid=008a2ca3592c4f8688a696b16736003a, punish_info=处以当事人梁文斌未在当地烟草专卖批发企业进货，进货总额人民币3310.00元6%的罚款，即罚款人民币198.6元。, org_uuid=4413231030000010543, org_tel=0752-7781678, post_code=516800, doc_date=1701878400000, org_abbr=龙门, case_time=1695657600000, sysisdelete=null, sys_create_time=[2023,12,25,10,46,35]}";
        return executeGatewayRequest("sv25213BWDRG", query);
    }

    @Override
    public JSONArray getEvidencePreservationNoticeDailyReport(Map<String, Object> query) {
		String originSampleData = """
			{
			  "WFGD": "《中华人民共和国烟草专卖法》及实施条例",
			  "FWZXSJGXSJ": {},
			  "DW": "条",
			  "KZZD3": {},
			  "GG": "84",
			  "FWZXSJSCBJ": {},
			  "XSBS": {},
			  "SJSSBM": "*******************",
			  "WPDJDJBS": "10a9a8645c2c40a0aac04cfe7cf88026",
			  "XH": 2,
			  "WSH": "59",
			  "AYUUID": "",
			  "ZXDWSL": "600.000000000000000000",
			  "AJMC": "梁*斌涉嫌未在当地烟草专卖批发企业进货",
			  "QXD": "441324",
			  "KZZD2": {},
			  "AJBH": "4413240202310059",
			  "ZZBH": {},
			  "CHHJ": "05",
			  "XYWYBS": "",
			  "CJSJ": 1695695946000,
			  "LYD": "441324",
			  "SJSSDW": "4413231030000010543",
			  "TAR": {},
			  "CJR": "陈*航",
			  "WPBS": "013980",
			  "SJBM": "10441300",
			  "XGSJ": 1725529455000,
			  "DJ": "350.000000000000000000",
			  "XGR": "陈*航",
			  "JE": "1050.000000000000000000",
			  "CBRUUIDS": "4413231223000010669,4413231223000005947",
			  "DJSJ": 1695657600000,
			  "WPFL": "11",
			  "PM": "芙蓉王(蓝)",
			  "BCQX": "七",
			  "SL": "3.000000000000000000",
			  "SFYX": 1,
			  "SFYJFSJCKXTL": 1,
			  "KZZD1": {},
			  "SQFYDW": "",
			  "WFTK": "《中华人民共和国行政处罚法》第五十六条",
			  "TTXM": "6901028193863",
			  "BZ": "",
			  "CKYDLSDM": {},
			  "XTCJSJ": 1695695946000,
			  "JGSX": "龙门",
			  "AY": "未在当地烟草专卖批发企业进货",
			  "WPMXBS": "128a6ff9bc0349ae9eb4452923898b32",
			  "DWZSL": 200,
			  "WSHQ": "龙门烟存通[2023]第59号",
			  "SJMC": "惠州市",
			  "AJBS": "008a2ca3592c4f8688a696b16736003a",
			  "XTGXSJ": 1725529455000,
			  "DSR": "梁*斌",
			  "SXXZZFL": "01",
			  "HJJE": "3310.000000000000000000",
			  "CBRZFZH": {},
			  "XBPBS": "6c12f84e22a441db877fc8d8e89ce5f8",
			  "JZSJ": {},
			  "JGJC": "广东省龙门县烟草专卖局",
			  "MSBTY": {},
			  "SXXZFL": "01",
			  "WSZT": {},
			  "HJSL": "13.000000000000000000",
			  "MCRKSJ": [
			    2025,
			    8,
			    7,
			    3,
			    13,
			    25
			  ],
			  "FYMC": "惠州市中级人民法院",
			  "CBR": "蒋*军(***********), 陈*航(***********)",
			  "JZR": {},
			  "ND": 2023
			}
			""";
        return executeGatewayRequest("sv252130CU5J", query);
    }

    @Override
    public JSONArray getCaseDeliveryReceiptDailyReport(Map<String, Object> query) {
		String originSampleData = """
			{
			  "FJID": {},
			  "SDNRSDWSMCBH": "[{\\"docUuid\\":\\"d4bff7b2f72242a6b1f5a61927758373\\",\\"docName\\":\\"准予许可决定书\\",\\"docNo\\":\\"惠阳烟专新〔2025〕许第184号\\"},{\\"docUuid\\":\\"d0a5fde6d578400298b78387136650cf\\",\\"docName\\":\\"烟草专卖零售许可证（正、副本）\\",\\"docNo\\":\\"************\\"}]",
			  "WSRQ": {},
			  "DSR": "邓**",
			  "SDNRJSONZFC": "[{\\"deliveryType\\":\\"01\\",\\"deliveryInfo\\":\\"158****5036\\"}]",
			  "SDR": "刘**(19090252022),张**(19090252034)",
			  "CJSJ": [2025, 3, 12, 10, 25, 18],
			  "SDHZBS": "608c6b7137e14f67ae883d751edba44f",
			  "WSSDFS": "02",
			  "SFQY": 1,
			  "SSRQZ": "蔡**",
			  "SQJLUUID": "9a134df05f0fd0a79c37488096fbb74d",
			  "SFJSDZSD": 1,
			  "XTGXSJCXBYDX": [2025, 3, 13, 14, 25, 13],
			  "SDRID": "4413231223000000676,4413231223000006431",
			  "XTCJSJCXBYDX": [2025, 3, 12, 10, 25, 18],
			  "DWJC": {},
			  "SFYJXD": 0,
			  "SFGGSD": 0,
			  "AJUUID": "0171bb1f7b774b7d956825a97702aba6",
			  "TAR": {},
			  "SSDRLXDH": "158****5036",
			  "XGR": {},
			  "CJR": {},
			  "BZ": "",
			  "SDFSBCSM": "",
			  "SDHZWSLX": "05",
			  "XFLSH": "",
			  "WSH": {},
			  "YWXSXWSMXBLSID": {},
			  "ZZJGUUID": "4413231030000000536",
			  "AJMC": "邓**涉嫌销售非法生产的烟草专卖品",
			  "ZMRQZRQ": {},
			  "DWSXZ": {},
			  "CBBMUUID": "4413240703000000148",
			  "AJJBXXBJGJC": "惠州市惠阳区烟草专卖局",
			  "WORDWSLJ": {},
			  "SSDR": "蔡**",
			  "ZMRQZ": "",
			  "XYWYBS": "",
			  "SDDD": "惠州市惠阳区淡水石桥潘屋曲江小区E1号***",
			  "SJBM": "10441300",
			  "ND": {},
			  "SSRQZRQ": 1741795200000,
			  "WCZT": {},
			  "SJMC": "惠州市",
			  "AJBH": "4413030202510028",
			  "MCRKSJ": [2025, 8, 7, 3, 20, 23],
			  "WSHQ": {},
			  "AJJBXXBJGSXZ": "惠阳",
			  "QSRQ": 1741795200000,
			  "CHBMUUID": "4413240703000000148",
			  "XGSJ": [2025, 3, 13, 14, 25, 13]
			}
			""";
//        return JSONUtil.parseArray(originSampleData);
        return executeGatewayRequest("sv25213NGUHC", query);
    }

    /**
     * 通用网关请求方法
     */
    private JSONArray executeGatewayRequest(String serviceId, Map<String, Object> query) {
        JSONObject requestBody = new JSONObject();
        requestBody.set("query", query);
        String queryStr = JSONUtil.toJsonStr(requestBody);

        String responseStr = mcGatewayHeaderService.executeRequest(serviceId, queryStr);

        if (responseStr == null) {
            return new JSONArray();
        }

        JSONObject response = JSONUtil.parseObj(responseStr);
        Integer errcode = response.getInt("errcode");

        if (errcode != null && errcode == 0) {
            JSONObject data = response.getJSONObject("data");
            if (data != null) {
                return data.getJSONArray("data");
            }
        }

        return new JSONArray();
    }
}

