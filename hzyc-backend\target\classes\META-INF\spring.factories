org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
  org.springblade.modules.resource.rule.oss.PreOssRule,\
  org.springblade.modules.resource.config.BladeSmsConfiguration,\
  org.springblade.modules.resource.rule.sms.PreSmsRule,\
  org.springblade.modules.system.service.impl.RegionServiceImpl,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.InterrogationRecordImpl,\
  org.springblade.modules.resource.rule.oss.OssTemplateRule,\
  org.springblade.modules.auth.granter.SmsTokenGranter,\
  org.springblade.modules.system.service.impl.AuthClientServiceImpl,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.CaseInfoServiceImpl,\
  org.springblade.modules.resource.rule.oss.OssReadRule,\
  org.springblade.modules.develop.service.impl.ModelPrototypeServiceImpl,\
  org.springblade.modules.resource.rule.sms.SmsBuildRule,\
  org.springblade.modules.system.controller.MenuController,\
  org.springblade.job.controller.JobInfoController,\
  org.springblade.modules.system.service.impl.TopMenuSettingServiceImpl,\
  org.springblade.modules.resource.rule.sms.AliSmsRule,\
  org.springblade.modules.hzyc.event.service.impl.EventServiceImpl,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.InquiryRecordDocumentImpl,\
  org.springblade.modules.system.controller.LogErrorController,\
  org.springblade.common.config.BladePreviewConfiguration,\
  org.springblade.modules.resource.rule.oss.AliOssRule,\
  org.springblade.flow.business.service.impl.FlowServiceImpl,\
  org.springblade.modules.resource.rule.oss.FinallyOssRule,\
  org.springblade.modules.develop.service.impl.CodeSettingServiceImpl,\
  org.springblade.flow.business.controller.WorkController,\
  org.springblade.modules.hzyc.document.service.impl.DocumentServiceImpl,\
  org.springblade.modules.hzyc.dify.controller.DifyController,\
  org.springblade.modules.system.service.impl.TenantPackageServiceImpl,\
  org.springblade.modules.develop.service.impl.DatasourceServiceImpl,\
  org.springblade.job.processor.ProcessorDemo,\
  org.springblade.modules.system.service.impl.UserServiceImpl,\
  org.springblade.modules.resource.config.BladeOssConfiguration,\
  org.springblade.modules.system.controller.SearchController,\
  org.springblade.modules.resource.rule.oss.HuaweiObsRule,\
  org.springblade.modules.system.service.impl.MenuServiceImpl,\
  org.springblade.modules.hzyc.DocumentGeneration.service.WordDocumentService,\
  org.springblade.Application,\
  org.springblade.flow.demo.leave.controller.LeaveController,\
  org.springblade.modules.resource.service.impl.OssServiceImpl,\
  org.springblade.modules.system.service.impl.DeptServiceImpl,\
  org.springblade.modules.system.controller.TenantController,\
  org.springblade.modules.resource.controller.OssController,\
  org.springblade.modules.hzyc.config.ResourceConfig,\
  org.springblade.modules.resource.rule.oss.LocalFileRule,\
  org.springblade.modules.resource.rule.oss.OssDataRule,\
  org.springblade.modules.resource.rule.builder.SmsRuleBuilder,\
  org.springblade.modules.resource.rule.oss.OssCacheRule,\
  org.springblade.modules.system.controller.ParamController,\
  org.springblade.modules.resource.controller.AttachController,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.CaseFilingReportDocumentImpl,\
  org.springblade.modules.system.rule.tenant.TenantRule,\
  org.springblade.modules.hzyc.document.controller.DocumentController,\
  org.springblade.modules.system.rule.tenant.TenantDeptRule,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.ReportRecordDocumentImpl,\
  org.springblade.modules.system.service.impl.LogServiceImpl,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.EvidenceCopyFormImpl,\
  org.springblade.modules.system.controller.DictBizController,\
  org.springblade.flow.business.service.impl.FlowBusinessServiceImpl,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.CaseApprovalDocumentImpl,\
  org.springblade.modules.develop.controller.ModelController,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.EvidencePreservationApprovalDocumentImpl,\
  org.springblade.modules.hzyc.cases.service.impl.CasesServiceImpl,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.GoodsPricingTableImpl,\
  org.springblade.modules.system.service.impl.UserDeptServiceImpl,\
  org.springblade.job.controller.JobServerController,\
  org.springblade.modules.hzyc.DocumentGeneration.controller.DocumentGenerationController,\
  org.springblade.modules.resource.controller.SmsController,\
  org.springblade.flow.engine.config.FlowableConfiguration,\
  org.springblade.modules.develop.service.impl.CodeServiceImpl,\
  org.springblade.common.config.BladeHandlerConfiguration,\
  org.springblade.modules.system.service.impl.ApiScopeServiceImpl,\
  org.springblade.modules.resource.rule.sms.FinallySmsRule,\
  org.springblade.modules.system.service.impl.RoleMenuServiceImpl,\
  org.springblade.modules.resource.rule.sms.TencentSmsRule,\
  org.springblade.modules.system.service.impl.RecordDataServiceImpl,\
  org.springblade.modules.system.service.impl.DictServiceImpl,\
  org.springblade.modules.hzyc.event.controller.EventController,\
  org.springblade.modules.hzyc.DocumentGeneration.controller.CaseInfoController,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.DeliveryReceiptDocumentImpl,\
  org.springblade.modules.system.service.impl.TenantServiceImpl,\
  org.springblade.modules.system.service.impl.DataScopeServiceImpl,\
  org.springblade.modules.system.rule.tenant.TenantUserRule,\
  org.springblade.modules.system.controller.ApiScopeController,\
  org.springblade.common.config.BladeLogConfiguration,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.EvidenceRegistrationDocumentImpl,\
  org.springblade.modules.system.rule.tenant.TenantRoleMenuRule,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.AdministrativeDecisionDocumentImpl,\
  org.springblade.modules.system.controller.RoleController,\
  org.springblade.modules.system.service.impl.ParamServiceImpl,\
  org.springblade.modules.develop.controller.ModelPrototypeController,\
  org.springblade.modules.resource.service.impl.AttachServiceImpl,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.CaseInvestigationReportImpl,\
  org.springblade.modules.resource.rule.sms.CacheSmsRule,\
  org.springblade.modules.auth.granter.CaptchaTokenGranter,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.CaseTransferLetterDocumentImpl,\
  org.springblade.modules.system.controller.TenantPackageController,\
  org.springblade.modules.resource.rule.sms.QiniuSmsRule,\
  org.springblade.modules.resource.endpoint.SmsEndpoint,\
  org.springblade.modules.system.controller.RegionController,\
  org.springblade.modules.system.controller.LogUsualController,\
  org.springblade.modules.system.service.impl.TopMenuServiceImpl,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.PenaltyDecisionDocumentImpl,\
  org.springblade.modules.system.controller.DeptController,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.CaseInitialReviewImpl,\
  org.springblade.flow.engine.service.impl.FlowEngineServiceImpl,\
  org.springblade.modules.system.service.impl.UserOauthServiceImpl,\
  org.springblade.job.service.impl.JobServerServiceImpl,\
  org.springblade.flow.engine.controller.FlowFollowController,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.SamplingEvidenceListImpl,\
  org.springblade.modules.develop.controller.DatasourceController,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.McGatewayHeaderServiceImpl,\
  org.springblade.flow.demo.leave.service.impl.LeaveServiceImpl,\
  org.springblade.common.config.SwaggerConfiguration,\
  org.springblade.modules.system.rule.tenant.TenantRoleRule,\
  org.springblade.modules.develop.service.impl.ModelServiceImpl,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.EvidenceProcessingNoticeImpl,\
  org.springblade.modules.system.controller.TenantDatasourceController,\
  org.springblade.modules.system.controller.LogApiController,\
  org.springblade.modules.desk.service.impl.NoticeServiceImpl,\
  org.springblade.modules.system.service.impl.UserSearchServiceImpl,\
  org.springblade.modules.develop.controller.CodeSettingController,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.GoodsReturnListImpl,\
  org.springblade.modules.system.service.impl.RoleServiceImpl,\
  org.springblade.modules.system.service.impl.LogUsualServiceImpl,\
  org.springblade.modules.auth.endpoint.Oauth2SmsEndpoint,\
  org.springblade.flow.engine.controller.FlowManagerController,\
  org.springblade.modules.resource.rule.sms.YunpianSmsRule,\
  org.springblade.modules.system.service.impl.LogErrorServiceImpl,\
  org.springblade.modules.hzyc.regulation.service.impl.RegulationServiceImpl,\
  org.springblade.modules.system.controller.UserController,\
  org.springblade.job.service.impl.JobInfoServiceImpl,\
  org.springblade.modules.auth.granter.RegisterTokenGranter,\
  org.springblade.modules.system.controller.DictController,\
  org.springblade.modules.resource.rule.oss.AmazonS3Rule,\
  org.springblade.modules.resource.service.impl.SmsServiceImpl,\
  org.springblade.flow.engine.controller.FlowModelController,\
  org.springblade.flow.engine.controller.FlowProcessController,\
  org.springblade.modules.resource.rule.oss.OssBuildRule,\
  org.springblade.modules.system.controller.PostController,\
  org.springblade.modules.resource.rule.builder.OssRuleBuilder,\
  org.springblade.modules.hzyc.dify.service.impl.DifyServiceImpl,\
  org.springblade.modules.system.controller.DataScopeController,\
  org.springblade.modules.desk.controller.NoticeController,\
  org.springblade.modules.system.service.impl.TenantDatasourceServiceImpl,\
  org.springblade.modules.resource.rule.oss.QiniuOssRule,\
  org.springblade.modules.system.rule.tenant.TenantDictBizRule,\
  org.springblade.common.config.BladeReportConfiguration,\
  org.springblade.modules.system.controller.RecordDataController,\
  org.springblade.modules.system.rule.tenant.TenantPostRule,\
  org.springblade.modules.auth.config.BladeAuthConfiguration,\
  org.springblade.modules.system.service.impl.DictBizServiceImpl,\
  org.springblade.common.config.BladeConfiguration,\
  org.springblade.modules.hzyc.cases.controller.CasesController,\
  org.springblade.modules.hzyc.regulation.controller.RegulationController,\
  org.springblade.modules.system.rule.builder.TenantRuleBuilder,\
  org.springblade.modules.auth.granter.SocialTokenGranter,\
  org.springblade.modules.resource.endpoint.OssEndpoint,\
  org.springblade.modules.resource.rule.oss.TencentCosRule,\
  org.springblade.modules.system.service.impl.PostServiceImpl,\
  org.springblade.modules.resource.rule.oss.MinioRule,\
  org.springblade.modules.develop.service.impl.GenerateServiceImpl,\
  org.springblade.modules.system.service.impl.RoleScopeServiceImpl,\
  org.springblade.modules.hzyc.DocumentGeneration.service.impl.PriorNoticeDocumentImpl,\
  org.springblade.modules.system.service.impl.LogApiServiceImpl,\
  org.springblade.modules.system.controller.AuthClientController,\
  org.springblade.modules.system.controller.TopMenuController,\
  org.springblade.modules.develop.controller.CodeController
